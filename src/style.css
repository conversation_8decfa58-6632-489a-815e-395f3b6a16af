@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    background: linear-gradient(135deg, #1f2937 0%, #111827 50%, #1f2937 100%);
    background-attachment: fixed;
    color: white;
    min-height: 100vh;
  }

  * {
    box-sizing: border-box;
  }
}

@layer components {
  .btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    font-weight: 600;
    padding: 12px 24px;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
    border: none;
    cursor: pointer;
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }

  .btn-secondary {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    color: white;
    font-weight: 600;
    padding: 12px 24px;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
    border: none;
    cursor: pointer;
  }

  .btn-secondary:hover {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    transform: scale(1.05);
  }

  .card {
    background: linear-gradient(135deg, rgba(31, 41, 55, 0.8) 0%, rgba(17, 24, 39, 0.8) 100%);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(75, 85, 99, 0.5);
    border-radius: 16px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  .game-card {
    padding: 24px;
    transition: all 0.3s;
    cursor: pointer;
  }

  .game-card:hover {
    transform: scale(1.05);
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
  }

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 2px solid transparent;
    border-bottom: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .mobile-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 50;
    background: black;
  }

  .game-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
    align-items: center;
    padding: 16px;
    background: linear-gradient(135deg, rgba(31, 41, 55, 0.9) 0%, rgba(55, 65, 81, 0.9) 100%);
    backdrop-filter: blur(8px);
    border-radius: 12px;
  }

  .status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 9999px;
    font-size: 14px;
    font-weight: 500;
  }

  .status-loading {
    background: rgba(234, 179, 8, 0.2);
    color: #fbbf24;
    border: 1px solid rgba(234, 179, 8, 0.3);
  }

  .status-playing {
    background: rgba(34, 197, 94, 0.2);
    color: #4ade80;
    border: 1px solid rgba(34, 197, 94, 0.3);
  }

  .status-error {
    background: rgba(239, 68, 68, 0.2);
    color: #f87171;
    border: 1px solid rgba(239, 68, 68, 0.3);
  }
}

@layer utilities {
  .text-gradient {
    background: linear-gradient(135deg, #60a5fa 0%, #2563eb 100%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .mobile-safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #2563eb;
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background: #3b82f6;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .mobile-optimized {
    font-size: 14px;
  }

  .mobile-padding {
    padding: 8px 16px;
  }

  /* 移动端游戏控制按钮优化 */
  .game-controls {
    padding: 12px;
    gap: 8px;
  }

  .game-controls button {
    padding: 8px 12px;
    font-size: 14px;
  }
}

/* 竖屏模式下的额外优化 */
@media (max-width: 768px) and (orientation: portrait) {
  .game-controls {
    padding: 16px;
    gap: 12px;
    flex-direction: column;
    align-items: stretch;
    height: 40vh;
    /* 控制按钮区域占40%高度 */
    justify-content: center;
    /* 垂直居中分布按钮 */
  }

  .game-controls button {
    padding: 16px;
    font-size: 18px;
    min-height: 56px;
    /* 确保按钮足够大，便于触摸 */
    border-radius: 12px;
  }

  /* 状态指示器在竖屏时居中显示 */
  .status-indicator {
    justify-content: center;
    width: 100%;
    margin-top: auto;
    /* 推到底部 */
  }
}

/* 游戏画布样式 */
#canvas {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 响应式游戏容器 */
.game-viewport {
  aspect-ratio: 4/3;
  max-height: 70vh;
}

@media (max-width: 768px) {
  .game-viewport {
    aspect-ratio: 16/9;
    max-height: none;
    height: 100vh;
    /* 默认占满屏幕 */
  }
}

/* 横屏模式优化 - 占满整个屏幕 */
@media (max-width: 768px) and (orientation: landscape) {
  .game-viewport {
    height: 100vh;
    /* 横屏时占满整个屏幕 */
    width: 100vw;
    aspect-ratio: 16/9;
  }
}

/* 竖屏模式优化 - 游戏画面占60%，下面留40%给控制按钮 */
@media (max-width: 768px) and (orientation: portrait) {
  .game-viewport {
    height: 98vh;
    /* 竖屏时游戏画面占60%高度 */
    /* width: 100vw; */
    /* 占满宽度 */
    aspect-ratio: auto;
    /* 自动比例，充分利用空间 */
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* 加载动画 */
.pulse-ring {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  border: 4px solid rgba(59, 130, 246, 0.3);
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes ping {

  75%,
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.pulse-ring:nth-child(2) {
  animation-delay: 0.5s;
}

.pulse-ring:nth-child(3) {
  animation-delay: 1s;
}