<template>
  <nav
    v-show="!shouldHideNavigation"
    class="glass-effect border-b border-white/10 sticky top-0 z-40"
  >
    <div class="max-w-6xl mx-auto px-4 py-3">
      <div class="flex items-center justify-between">
        <!-- Logo -->
        <router-link
          to="/"
          class="flex items-center gap-2 text-xl font-bold text-gradient hover:scale-105 transition-transform"
        >
          <span class="text-2xl">🎮</span>
          游戏模拟器
        </router-link>

        <!-- 桌面端导航菜单 -->
        <div class="hidden md:flex items-center gap-6">
          <router-link
            to="/"
            class="nav-link"
            :class="{ 'nav-link-active': $route.name === 'Home' }"
          >
            首页
          </router-link>

          <router-link
            to="/library"
            class="nav-link"
            :class="{ 'nav-link-active': $route.name === 'GameLibrary' }"
          >
            游戏库
          </router-link>

          <router-link
            to="/about"
            class="nav-link"
            :class="{ 'nav-link-active': $route.name === 'About' }"
          >
            关于
          </router-link>
        </div>

        <!-- 移动端菜单按钮 -->
        <button
          @click="toggleMobileMenu"
          class="md:hidden p-2 rounded-lg hover:bg-white/10 transition-colors"
        >
          <svg
            v-if="!isMobileMenuOpen"
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"
            ></path>
          </svg>
          <svg
            v-else
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
        </button>
      </div>

      <!-- 移动端菜单 -->
      <div
        v-if="isMobileMenuOpen"
        class="md:hidden mt-4 pb-4 border-t border-white/10 pt-4"
      >
        <div class="flex flex-col gap-2">
          <router-link
            to="/"
            class="mobile-nav-link"
            :class="{ 'mobile-nav-link-active': $route.name === 'Home' }"
            @click="closeMobileMenu"
          >
            🏠 首页
          </router-link>

          <router-link
            to="/library"
            class="mobile-nav-link"
            :class="{ 'mobile-nav-link-active': $route.name === 'GameLibrary' }"
            @click="closeMobileMenu"
          >
            🎮 游戏库
          </router-link>

          <router-link
            to="/about"
            class="mobile-nav-link"
            :class="{ 'mobile-nav-link-active': $route.name === 'About' }"
            @click="closeMobileMenu"
          >
            📖 关于
          </router-link>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref } from "vue";

const isMobileMenuOpen = ref(false);

function toggleMobileMenu() {
  isMobileMenuOpen.value = !isMobileMenuOpen.value;
}

function closeMobileMenu() {
  isMobileMenuOpen.value = false;
}
</script>

<style scoped>
.nav-link {
  @apply px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-colors;
}

.nav-link-active {
  @apply text-white bg-primary-500/20 border border-primary-500/30;
}

.mobile-nav-link {
  @apply block px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-colors;
}

.mobile-nav-link-active {
  @apply text-white bg-primary-500/20 border border-primary-500/30;
}
</style>
